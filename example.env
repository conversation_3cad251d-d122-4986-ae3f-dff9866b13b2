FRONTEND_URL=http://localhost:3000

# Only DATABASE_URL is required, but can be composed of different postgres values
POSTGRES_USER=openship
POSTGRES_PASSWORD=openshipasswd
POSTGRES_DB=openship
DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@**********:5432/${POSTGRES_DB}
SESSION_SECRET=OH PLEASE PLEASE CHANGE ME

# If true, anyone can sign-up otherwise only admin can create users
ALLOW_EXTERNAL_SIGNUPS

# Email
SMTP_HOST
SMTP_PORT
SMTP_USER
SMTP_PASSWORD
SMTP_FROM

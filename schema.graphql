# This file is automatically generated by Keystone, do not modify it manually.
# Modify your Keystone config when you want to change this.

type User {
  id: ID!
  name: String
  email: String
  password: PasswordState
  shops(where: ShopWhereInput! = {}, orderBy: [ShopOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShopWhereUniqueInput): [Shop!]
  shopsCount(where: ShopWhereInput! = {}): Int
  shopPlatforms(where: ShopPlatformWhereInput! = {}, orderBy: [ShopPlatformOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShopPlatformWhereUniqueInput): [ShopPlatform!]
  shopPlatformsCount(where: ShopPlatformWhereInput! = {}): Int
  channels(where: ChannelWhereInput! = {}, orderBy: [ChannelOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ChannelWhereUniqueInput): [Channel!]
  channelsCount(where: ChannelWhereInput! = {}): Int
  channelPlatforms(where: ChannelPlatformWhereInput! = {}, orderBy: [ChannelPlatformOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ChannelPlatformWhereUniqueInput): [ChannelPlatform!]
  channelPlatformsCount(where: ChannelPlatformWhereInput! = {}): Int
  orders(where: OrderWhereInput! = {}, orderBy: [OrderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: OrderWhereUniqueInput): [Order!]
  ordersCount(where: OrderWhereInput! = {}): Int
  lineItems(where: LineItemWhereInput! = {}, orderBy: [LineItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: LineItemWhereUniqueInput): [LineItem!]
  lineItemsCount(where: LineItemWhereInput! = {}): Int
  cartItems(where: CartItemWhereInput! = {}, orderBy: [CartItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CartItemWhereUniqueInput): [CartItem!]
  cartItemsCount(where: CartItemWhereInput! = {}): Int
  shopItems(where: ShopItemWhereInput! = {}, orderBy: [ShopItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShopItemWhereUniqueInput): [ShopItem!]
  shopItemsCount(where: ShopItemWhereInput! = {}): Int
  channelItems(where: ChannelItemWhereInput! = {}, orderBy: [ChannelItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ChannelItemWhereUniqueInput): [ChannelItem!]
  channelItemsCount(where: ChannelItemWhereInput! = {}): Int
  apiKeys(where: apiKeyWhereInput! = {}, orderBy: [apiKeyOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: apiKeyWhereUniqueInput): [apiKey!]
  apiKeysCount(where: apiKeyWhereInput! = {}): Int
  matches(where: MatchWhereInput! = {}, orderBy: [MatchOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: MatchWhereUniqueInput): [Match!]
  matchesCount(where: MatchWhereInput! = {}): Int
  links(where: LinkWhereInput! = {}, orderBy: [LinkOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: LinkWhereUniqueInput): [Link!]
  linksCount(where: LinkWhereInput! = {}): Int
  trackingDetails(where: TrackingDetailWhereInput! = {}, orderBy: [TrackingDetailOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: TrackingDetailWhereUniqueInput): [TrackingDetail!]
  trackingDetailsCount(where: TrackingDetailWhereInput! = {}): Int
  role: Role
  createdAt: DateTime
  updatedAt: DateTime
  passwordResetToken: PasswordState
  passwordResetIssuedAt: DateTime
  passwordResetRedeemedAt: DateTime
}

type PasswordState {
  isSet: Boolean!
}

scalar DateTime @specifiedBy(url: "https://datatracker.ietf.org/doc/html/rfc3339#section-5.6")

input UserWhereUniqueInput {
  id: ID
  email: String
}

input UserWhereInput {
  AND: [UserWhereInput!]
  OR: [UserWhereInput!]
  NOT: [UserWhereInput!]
  id: IDFilter
  name: StringFilter
  email: StringFilter
  shops: ShopManyRelationFilter
  shopPlatforms: ShopPlatformManyRelationFilter
  channels: ChannelManyRelationFilter
  channelPlatforms: ChannelPlatformManyRelationFilter
  orders: OrderManyRelationFilter
  lineItems: LineItemManyRelationFilter
  cartItems: CartItemManyRelationFilter
  shopItems: ShopItemManyRelationFilter
  channelItems: ChannelItemManyRelationFilter
  apiKeys: apiKeyManyRelationFilter
  matches: MatchManyRelationFilter
  links: LinkManyRelationFilter
  trackingDetails: TrackingDetailManyRelationFilter
  role: RoleWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
  passwordResetToken: PasswordFilter
  passwordResetIssuedAt: DateTimeNullableFilter
  passwordResetRedeemedAt: DateTimeNullableFilter
}

input IDFilter {
  equals: ID
  in: [ID!]
  notIn: [ID!]
  lt: ID
  lte: ID
  gt: ID
  gte: ID
  not: IDFilter
}

input StringFilter {
  equals: String
  in: [String!]
  notIn: [String!]
  lt: String
  lte: String
  gt: String
  gte: String
  contains: String
  startsWith: String
  endsWith: String
  mode: QueryMode
  not: NestedStringFilter
}

enum QueryMode {
  default
  insensitive
}

input NestedStringFilter {
  equals: String
  in: [String!]
  notIn: [String!]
  lt: String
  lte: String
  gt: String
  gte: String
  contains: String
  startsWith: String
  endsWith: String
  not: NestedStringFilter
}

input ShopManyRelationFilter {
  every: ShopWhereInput
  some: ShopWhereInput
  none: ShopWhereInput
}

input ShopPlatformManyRelationFilter {
  every: ShopPlatformWhereInput
  some: ShopPlatformWhereInput
  none: ShopPlatformWhereInput
}

input ChannelManyRelationFilter {
  every: ChannelWhereInput
  some: ChannelWhereInput
  none: ChannelWhereInput
}

input ChannelPlatformManyRelationFilter {
  every: ChannelPlatformWhereInput
  some: ChannelPlatformWhereInput
  none: ChannelPlatformWhereInput
}

input OrderManyRelationFilter {
  every: OrderWhereInput
  some: OrderWhereInput
  none: OrderWhereInput
}

input LineItemManyRelationFilter {
  every: LineItemWhereInput
  some: LineItemWhereInput
  none: LineItemWhereInput
}

input CartItemManyRelationFilter {
  every: CartItemWhereInput
  some: CartItemWhereInput
  none: CartItemWhereInput
}

input ShopItemManyRelationFilter {
  every: ShopItemWhereInput
  some: ShopItemWhereInput
  none: ShopItemWhereInput
}

input ChannelItemManyRelationFilter {
  every: ChannelItemWhereInput
  some: ChannelItemWhereInput
  none: ChannelItemWhereInput
}

input apiKeyManyRelationFilter {
  every: apiKeyWhereInput
  some: apiKeyWhereInput
  none: apiKeyWhereInput
}

input MatchManyRelationFilter {
  every: MatchWhereInput
  some: MatchWhereInput
  none: MatchWhereInput
}

input LinkManyRelationFilter {
  every: LinkWhereInput
  some: LinkWhereInput
  none: LinkWhereInput
}

input TrackingDetailManyRelationFilter {
  every: TrackingDetailWhereInput
  some: TrackingDetailWhereInput
  none: TrackingDetailWhereInput
}

input DateTimeFilter {
  equals: DateTime
  in: [DateTime!]
  notIn: [DateTime!]
  lt: DateTime
  lte: DateTime
  gt: DateTime
  gte: DateTime
  not: DateTimeFilter
}

input PasswordFilter {
  isSet: Boolean!
}

input DateTimeNullableFilter {
  equals: DateTime
  in: [DateTime!]
  notIn: [DateTime!]
  lt: DateTime
  lte: DateTime
  gt: DateTime
  gte: DateTime
  not: DateTimeNullableFilter
}

input UserOrderByInput {
  id: OrderDirection
  name: OrderDirection
  email: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
  passwordResetIssuedAt: OrderDirection
  passwordResetRedeemedAt: OrderDirection
}

enum OrderDirection {
  asc
  desc
}

input UserUpdateInput {
  name: String
  email: String
  password: String
  shops: ShopRelateToManyForUpdateInput
  shopPlatforms: ShopPlatformRelateToManyForUpdateInput
  channels: ChannelRelateToManyForUpdateInput
  channelPlatforms: ChannelPlatformRelateToManyForUpdateInput
  orders: OrderRelateToManyForUpdateInput
  lineItems: LineItemRelateToManyForUpdateInput
  cartItems: CartItemRelateToManyForUpdateInput
  shopItems: ShopItemRelateToManyForUpdateInput
  channelItems: ChannelItemRelateToManyForUpdateInput
  apiKeys: apiKeyRelateToManyForUpdateInput
  matches: MatchRelateToManyForUpdateInput
  links: LinkRelateToManyForUpdateInput
  trackingDetails: TrackingDetailRelateToManyForUpdateInput
  role: RoleRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
  passwordResetToken: String
  passwordResetIssuedAt: DateTime
  passwordResetRedeemedAt: DateTime
}

input ShopRelateToManyForUpdateInput {
  disconnect: [ShopWhereUniqueInput!]
  set: [ShopWhereUniqueInput!]
  create: [ShopCreateInput!]
  connect: [ShopWhereUniqueInput!]
}

input ShopPlatformRelateToManyForUpdateInput {
  disconnect: [ShopPlatformWhereUniqueInput!]
  set: [ShopPlatformWhereUniqueInput!]
  create: [ShopPlatformCreateInput!]
  connect: [ShopPlatformWhereUniqueInput!]
}

input ChannelRelateToManyForUpdateInput {
  disconnect: [ChannelWhereUniqueInput!]
  set: [ChannelWhereUniqueInput!]
  create: [ChannelCreateInput!]
  connect: [ChannelWhereUniqueInput!]
}

input ChannelPlatformRelateToManyForUpdateInput {
  disconnect: [ChannelPlatformWhereUniqueInput!]
  set: [ChannelPlatformWhereUniqueInput!]
  create: [ChannelPlatformCreateInput!]
  connect: [ChannelPlatformWhereUniqueInput!]
}

input OrderRelateToManyForUpdateInput {
  disconnect: [OrderWhereUniqueInput!]
  set: [OrderWhereUniqueInput!]
  create: [OrderCreateInput!]
  connect: [OrderWhereUniqueInput!]
}

input LineItemRelateToManyForUpdateInput {
  disconnect: [LineItemWhereUniqueInput!]
  set: [LineItemWhereUniqueInput!]
  create: [LineItemCreateInput!]
  connect: [LineItemWhereUniqueInput!]
}

input CartItemRelateToManyForUpdateInput {
  disconnect: [CartItemWhereUniqueInput!]
  set: [CartItemWhereUniqueInput!]
  create: [CartItemCreateInput!]
  connect: [CartItemWhereUniqueInput!]
}

input ShopItemRelateToManyForUpdateInput {
  disconnect: [ShopItemWhereUniqueInput!]
  set: [ShopItemWhereUniqueInput!]
  create: [ShopItemCreateInput!]
  connect: [ShopItemWhereUniqueInput!]
}

input ChannelItemRelateToManyForUpdateInput {
  disconnect: [ChannelItemWhereUniqueInput!]
  set: [ChannelItemWhereUniqueInput!]
  create: [ChannelItemCreateInput!]
  connect: [ChannelItemWhereUniqueInput!]
}

input apiKeyRelateToManyForUpdateInput {
  disconnect: [apiKeyWhereUniqueInput!]
  set: [apiKeyWhereUniqueInput!]
  create: [apiKeyCreateInput!]
  connect: [apiKeyWhereUniqueInput!]
}

input MatchRelateToManyForUpdateInput {
  disconnect: [MatchWhereUniqueInput!]
  set: [MatchWhereUniqueInput!]
  create: [MatchCreateInput!]
  connect: [MatchWhereUniqueInput!]
}

input LinkRelateToManyForUpdateInput {
  disconnect: [LinkWhereUniqueInput!]
  set: [LinkWhereUniqueInput!]
  create: [LinkCreateInput!]
  connect: [LinkWhereUniqueInput!]
}

input TrackingDetailRelateToManyForUpdateInput {
  disconnect: [TrackingDetailWhereUniqueInput!]
  set: [TrackingDetailWhereUniqueInput!]
  create: [TrackingDetailCreateInput!]
  connect: [TrackingDetailWhereUniqueInput!]
}

input RoleRelateToOneForUpdateInput {
  create: RoleCreateInput
  connect: RoleWhereUniqueInput
  disconnect: Boolean
}

input UserUpdateArgs {
  where: UserWhereUniqueInput!
  data: UserUpdateInput!
}

input UserCreateInput {
  name: String
  email: String
  password: String
  shops: ShopRelateToManyForCreateInput
  shopPlatforms: ShopPlatformRelateToManyForCreateInput
  channels: ChannelRelateToManyForCreateInput
  channelPlatforms: ChannelPlatformRelateToManyForCreateInput
  orders: OrderRelateToManyForCreateInput
  lineItems: LineItemRelateToManyForCreateInput
  cartItems: CartItemRelateToManyForCreateInput
  shopItems: ShopItemRelateToManyForCreateInput
  channelItems: ChannelItemRelateToManyForCreateInput
  apiKeys: apiKeyRelateToManyForCreateInput
  matches: MatchRelateToManyForCreateInput
  links: LinkRelateToManyForCreateInput
  trackingDetails: TrackingDetailRelateToManyForCreateInput
  role: RoleRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
  passwordResetToken: String
  passwordResetIssuedAt: DateTime
  passwordResetRedeemedAt: DateTime
}

input ShopRelateToManyForCreateInput {
  create: [ShopCreateInput!]
  connect: [ShopWhereUniqueInput!]
}

input ShopPlatformRelateToManyForCreateInput {
  create: [ShopPlatformCreateInput!]
  connect: [ShopPlatformWhereUniqueInput!]
}

input ChannelRelateToManyForCreateInput {
  create: [ChannelCreateInput!]
  connect: [ChannelWhereUniqueInput!]
}

input ChannelPlatformRelateToManyForCreateInput {
  create: [ChannelPlatformCreateInput!]
  connect: [ChannelPlatformWhereUniqueInput!]
}

input OrderRelateToManyForCreateInput {
  create: [OrderCreateInput!]
  connect: [OrderWhereUniqueInput!]
}

input LineItemRelateToManyForCreateInput {
  create: [LineItemCreateInput!]
  connect: [LineItemWhereUniqueInput!]
}

input CartItemRelateToManyForCreateInput {
  create: [CartItemCreateInput!]
  connect: [CartItemWhereUniqueInput!]
}

input ShopItemRelateToManyForCreateInput {
  create: [ShopItemCreateInput!]
  connect: [ShopItemWhereUniqueInput!]
}

input ChannelItemRelateToManyForCreateInput {
  create: [ChannelItemCreateInput!]
  connect: [ChannelItemWhereUniqueInput!]
}

input apiKeyRelateToManyForCreateInput {
  create: [apiKeyCreateInput!]
  connect: [apiKeyWhereUniqueInput!]
}

input MatchRelateToManyForCreateInput {
  create: [MatchCreateInput!]
  connect: [MatchWhereUniqueInput!]
}

input LinkRelateToManyForCreateInput {
  create: [LinkCreateInput!]
  connect: [LinkWhereUniqueInput!]
}

input TrackingDetailRelateToManyForCreateInput {
  create: [TrackingDetailCreateInput!]
  connect: [TrackingDetailWhereUniqueInput!]
}

input RoleRelateToOneForCreateInput {
  create: RoleCreateInput
  connect: RoleWhereUniqueInput
}

type apiKey {
  id: ID!
  user: User
  createdAt: DateTime
  updatedAt: DateTime
}

input apiKeyWhereUniqueInput {
  id: ID
}

input apiKeyWhereInput {
  AND: [apiKeyWhereInput!]
  OR: [apiKeyWhereInput!]
  NOT: [apiKeyWhereInput!]
  id: IDFilter
  user: UserWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input apiKeyOrderByInput {
  id: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input apiKeyUpdateInput {
  user: UserRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input UserRelateToOneForUpdateInput {
  create: UserCreateInput
  connect: UserWhereUniqueInput
  disconnect: Boolean
}

input apiKeyUpdateArgs {
  where: apiKeyWhereUniqueInput!
  data: apiKeyUpdateInput!
}

input apiKeyCreateInput {
  user: UserRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input UserRelateToOneForCreateInput {
  create: UserCreateInput
  connect: UserWhereUniqueInput
}

type Role {
  id: ID!
  name: String
  canSeeOtherUsers: Boolean
  canManageUsers: Boolean
  canManageRoles: Boolean
  canSeeOtherOrders: Boolean
  canManageOrders: Boolean
  canSeeOtherShops: Boolean
  canManageShops: Boolean
  canSeeOtherChannels: Boolean
  canManageChannels: Boolean
  canSeeOtherMatches: Boolean
  canManageMatches: Boolean
  canSeeOtherLinks: Boolean
  canManageLinks: Boolean
  assignedTo(where: UserWhereInput! = {}, orderBy: [UserOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: UserWhereUniqueInput): [User!]
  assignedToCount(where: UserWhereInput! = {}): Int
}

input RoleWhereUniqueInput {
  id: ID
}

input RoleWhereInput {
  AND: [RoleWhereInput!]
  OR: [RoleWhereInput!]
  NOT: [RoleWhereInput!]
  id: IDFilter
  name: StringFilter
  canSeeOtherUsers: BooleanFilter
  canManageUsers: BooleanFilter
  canManageRoles: BooleanFilter
  canSeeOtherOrders: BooleanFilter
  canManageOrders: BooleanFilter
  canSeeOtherShops: BooleanFilter
  canManageShops: BooleanFilter
  canSeeOtherChannels: BooleanFilter
  canManageChannels: BooleanFilter
  canSeeOtherMatches: BooleanFilter
  canManageMatches: BooleanFilter
  canSeeOtherLinks: BooleanFilter
  canManageLinks: BooleanFilter
  assignedTo: UserManyRelationFilter
}

input BooleanFilter {
  equals: Boolean
  not: BooleanFilter
}

input UserManyRelationFilter {
  every: UserWhereInput
  some: UserWhereInput
  none: UserWhereInput
}

input RoleOrderByInput {
  id: OrderDirection
  name: OrderDirection
  canSeeOtherUsers: OrderDirection
  canManageUsers: OrderDirection
  canManageRoles: OrderDirection
  canSeeOtherOrders: OrderDirection
  canManageOrders: OrderDirection
  canSeeOtherShops: OrderDirection
  canManageShops: OrderDirection
  canSeeOtherChannels: OrderDirection
  canManageChannels: OrderDirection
  canSeeOtherMatches: OrderDirection
  canManageMatches: OrderDirection
  canSeeOtherLinks: OrderDirection
  canManageLinks: OrderDirection
}

input RoleUpdateInput {
  name: String
  canSeeOtherUsers: Boolean
  canManageUsers: Boolean
  canManageRoles: Boolean
  canSeeOtherOrders: Boolean
  canManageOrders: Boolean
  canSeeOtherShops: Boolean
  canManageShops: Boolean
  canSeeOtherChannels: Boolean
  canManageChannels: Boolean
  canSeeOtherMatches: Boolean
  canManageMatches: Boolean
  canSeeOtherLinks: Boolean
  canManageLinks: Boolean
  assignedTo: UserRelateToManyForUpdateInput
}

input UserRelateToManyForUpdateInput {
  disconnect: [UserWhereUniqueInput!]
  set: [UserWhereUniqueInput!]
  create: [UserCreateInput!]
  connect: [UserWhereUniqueInput!]
}

input RoleUpdateArgs {
  where: RoleWhereUniqueInput!
  data: RoleUpdateInput!
}

input RoleCreateInput {
  name: String
  canSeeOtherUsers: Boolean
  canManageUsers: Boolean
  canManageRoles: Boolean
  canSeeOtherOrders: Boolean
  canManageOrders: Boolean
  canSeeOtherShops: Boolean
  canManageShops: Boolean
  canSeeOtherChannels: Boolean
  canManageChannels: Boolean
  canSeeOtherMatches: Boolean
  canManageMatches: Boolean
  canSeeOtherLinks: Boolean
  canManageLinks: Boolean
  assignedTo: UserRelateToManyForCreateInput
}

input UserRelateToManyForCreateInput {
  create: [UserCreateInput!]
  connect: [UserWhereUniqueInput!]
}

type Order {
  id: ID!
  orderId: String
  orderLink: String
  orderName: String
  email: String
  firstName: String
  lastName: String
  streetAddress1: String
  streetAddress2: String
  city: String
  state: String
  zip: String
  currency: String
  totalPrice: String
  subTotalPrice: String
  totalDiscount: String
  totalTax: String
  phoneNumber: String
  note: String
  shippingMethod: JSON
  country: String
  orderError: String
  status: String
  locationId: Float
  user: User
  shop: Shop
  lineItems(where: LineItemWhereInput! = {}, orderBy: [LineItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: LineItemWhereUniqueInput): [LineItem!]
  lineItemsCount(where: LineItemWhereInput! = {}): Int
  cartItems(where: CartItemWhereInput! = {}, orderBy: [CartItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CartItemWhereUniqueInput): [CartItem!]
  cartItemsCount(where: CartItemWhereInput! = {}): Int
  linkOrder: Boolean
  matchOrder: Boolean
  processOrder: Boolean
  readyToProcess: String
  createdAt: DateTime
  updatedAt: DateTime
}

input OrderWhereUniqueInput {
  id: ID
}

input OrderWhereInput {
  AND: [OrderWhereInput!]
  OR: [OrderWhereInput!]
  NOT: [OrderWhereInput!]
  id: IDFilter
  orderId: StringFilter
  orderName: StringFilter
  email: StringFilter
  firstName: StringFilter
  lastName: StringFilter
  streetAddress1: StringFilter
  streetAddress2: StringFilter
  city: StringFilter
  state: StringFilter
  zip: StringFilter
  currency: StringFilter
  totalPrice: StringFilter
  subTotalPrice: StringFilter
  totalDiscount: StringFilter
  totalTax: StringFilter
  phoneNumber: StringFilter
  note: StringFilter
  country: StringFilter
  orderError: StringFilter
  status: StringFilter
  locationId: FloatNullableFilter
  user: UserWhereInput
  shop: ShopWhereInput
  lineItems: LineItemManyRelationFilter
  cartItems: CartItemManyRelationFilter
  linkOrder: BooleanFilter
  matchOrder: BooleanFilter
  processOrder: BooleanFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input FloatNullableFilter {
  equals: Float
  in: [Float!]
  notIn: [Float!]
  lt: Float
  lte: Float
  gt: Float
  gte: Float
  not: FloatNullableFilter
}

input OrderOrderByInput {
  id: OrderDirection
  orderId: OrderDirection
  orderName: OrderDirection
  email: OrderDirection
  firstName: OrderDirection
  lastName: OrderDirection
  streetAddress1: OrderDirection
  streetAddress2: OrderDirection
  city: OrderDirection
  state: OrderDirection
  zip: OrderDirection
  currency: OrderDirection
  totalPrice: OrderDirection
  subTotalPrice: OrderDirection
  totalDiscount: OrderDirection
  totalTax: OrderDirection
  phoneNumber: OrderDirection
  note: OrderDirection
  country: OrderDirection
  orderError: OrderDirection
  status: OrderDirection
  locationId: OrderDirection
  linkOrder: OrderDirection
  matchOrder: OrderDirection
  processOrder: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input OrderUpdateInput {
  orderId: String
  orderName: String
  email: String
  firstName: String
  lastName: String
  streetAddress1: String
  streetAddress2: String
  city: String
  state: String
  zip: String
  currency: String
  totalPrice: String
  subTotalPrice: String
  totalDiscount: String
  totalTax: String
  phoneNumber: String
  note: String
  shippingMethod: JSON
  country: String
  orderError: String
  status: String
  locationId: Float
  user: UserRelateToOneForUpdateInput
  shop: ShopRelateToOneForUpdateInput
  lineItems: LineItemRelateToManyForUpdateInput
  cartItems: CartItemRelateToManyForUpdateInput
  linkOrder: Boolean
  matchOrder: Boolean
  processOrder: Boolean
  createdAt: DateTime
  updatedAt: DateTime
}

input ShopRelateToOneForUpdateInput {
  create: ShopCreateInput
  connect: ShopWhereUniqueInput
  disconnect: Boolean
}

input OrderUpdateArgs {
  where: OrderWhereUniqueInput!
  data: OrderUpdateInput!
}

input OrderCreateInput {
  orderId: String
  orderName: String
  email: String
  firstName: String
  lastName: String
  streetAddress1: String
  streetAddress2: String
  city: String
  state: String
  zip: String
  currency: String
  totalPrice: String
  subTotalPrice: String
  totalDiscount: String
  totalTax: String
  phoneNumber: String
  note: String
  shippingMethod: JSON
  country: String
  orderError: String
  status: String
  locationId: Float
  user: UserRelateToOneForCreateInput
  shop: ShopRelateToOneForCreateInput
  lineItems: LineItemRelateToManyForCreateInput
  cartItems: CartItemRelateToManyForCreateInput
  linkOrder: Boolean
  matchOrder: Boolean
  processOrder: Boolean
  createdAt: DateTime
  updatedAt: DateTime
}

input ShopRelateToOneForCreateInput {
  create: ShopCreateInput
  connect: ShopWhereUniqueInput
}

type TrackingDetail {
  id: ID!
  trackingCompany: String
  trackingNumber: String
  purchaseId: String
  cartItems(where: CartItemWhereInput! = {}, orderBy: [CartItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CartItemWhereUniqueInput): [CartItem!]
  cartItemsCount(where: CartItemWhereInput! = {}): Int
  user: User
  createdAt: DateTime
  updatedAt: DateTime
}

input TrackingDetailWhereUniqueInput {
  id: ID
}

input TrackingDetailWhereInput {
  AND: [TrackingDetailWhereInput!]
  OR: [TrackingDetailWhereInput!]
  NOT: [TrackingDetailWhereInput!]
  id: IDFilter
  trackingCompany: StringFilter
  trackingNumber: StringFilter
  purchaseId: StringFilter
  cartItems: CartItemManyRelationFilter
  user: UserWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input TrackingDetailOrderByInput {
  id: OrderDirection
  trackingCompany: OrderDirection
  trackingNumber: OrderDirection
  purchaseId: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input TrackingDetailUpdateInput {
  trackingCompany: String
  trackingNumber: String
  purchaseId: String
  cartItems: CartItemRelateToManyForUpdateInput
  user: UserRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input TrackingDetailUpdateArgs {
  where: TrackingDetailWhereUniqueInput!
  data: TrackingDetailUpdateInput!
}

input TrackingDetailCreateInput {
  trackingCompany: String
  trackingNumber: String
  purchaseId: String
  cartItems: CartItemRelateToManyForCreateInput
  user: UserRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type LineItem {
  id: ID!
  name: String
  image: String
  price: String
  quantity: Int
  productId: String
  variantId: String
  sku: String
  lineItemId: String
  order: Order
  user: User
  createdAt: DateTime
  updatedAt: DateTime
}

input LineItemWhereUniqueInput {
  id: ID
}

input LineItemWhereInput {
  AND: [LineItemWhereInput!]
  OR: [LineItemWhereInput!]
  NOT: [LineItemWhereInput!]
  id: IDFilter
  name: StringFilter
  image: StringFilter
  price: StringFilter
  quantity: IntNullableFilter
  productId: StringFilter
  variantId: StringFilter
  sku: StringFilter
  lineItemId: StringFilter
  order: OrderWhereInput
  user: UserWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input IntNullableFilter {
  equals: Int
  in: [Int!]
  notIn: [Int!]
  lt: Int
  lte: Int
  gt: Int
  gte: Int
  not: IntNullableFilter
}

input LineItemOrderByInput {
  id: OrderDirection
  name: OrderDirection
  image: OrderDirection
  price: OrderDirection
  quantity: OrderDirection
  productId: OrderDirection
  variantId: OrderDirection
  sku: OrderDirection
  lineItemId: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input LineItemUpdateInput {
  name: String
  image: String
  price: String
  quantity: Int
  productId: String
  variantId: String
  sku: String
  lineItemId: String
  order: OrderRelateToOneForUpdateInput
  user: UserRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input OrderRelateToOneForUpdateInput {
  create: OrderCreateInput
  connect: OrderWhereUniqueInput
  disconnect: Boolean
}

input LineItemUpdateArgs {
  where: LineItemWhereUniqueInput!
  data: LineItemUpdateInput!
}

input LineItemCreateInput {
  name: String
  image: String
  price: String
  quantity: Int
  productId: String
  variantId: String
  sku: String
  lineItemId: String
  order: OrderRelateToOneForCreateInput
  user: UserRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input OrderRelateToOneForCreateInput {
  create: OrderCreateInput
  connect: OrderWhereUniqueInput
}

type CartItem {
  id: ID!
  name: String
  image: String
  price: String
  quantity: Int
  productId: String
  variantId: String
  lineItemId: String
  sku: String
  url: String
  error: String
  purchaseId: String
  status: String
  order: Order
  trackingDetails(where: TrackingDetailWhereInput! = {}, orderBy: [TrackingDetailOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: TrackingDetailWhereUniqueInput): [TrackingDetail!]
  trackingDetailsCount(where: TrackingDetailWhereInput! = {}): Int
  user: User
  channel: Channel
  createdAt: DateTime
  updatedAt: DateTime
}

input CartItemWhereUniqueInput {
  id: ID
}

input CartItemWhereInput {
  AND: [CartItemWhereInput!]
  OR: [CartItemWhereInput!]
  NOT: [CartItemWhereInput!]
  id: IDFilter
  name: StringFilter
  image: StringFilter
  price: StringFilter
  quantity: IntNullableFilter
  productId: StringFilter
  variantId: StringFilter
  lineItemId: StringFilter
  sku: StringFilter
  url: StringFilter
  error: StringFilter
  purchaseId: StringFilter
  status: StringFilter
  order: OrderWhereInput
  trackingDetails: TrackingDetailManyRelationFilter
  user: UserWhereInput
  channel: ChannelWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input CartItemOrderByInput {
  id: OrderDirection
  name: OrderDirection
  image: OrderDirection
  price: OrderDirection
  quantity: OrderDirection
  productId: OrderDirection
  variantId: OrderDirection
  lineItemId: OrderDirection
  sku: OrderDirection
  url: OrderDirection
  error: OrderDirection
  purchaseId: OrderDirection
  status: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input CartItemUpdateInput {
  name: String
  image: String
  price: String
  quantity: Int
  productId: String
  variantId: String
  lineItemId: String
  sku: String
  url: String
  error: String
  purchaseId: String
  status: String
  order: OrderRelateToOneForUpdateInput
  trackingDetails: TrackingDetailRelateToManyForUpdateInput
  user: UserRelateToOneForUpdateInput
  channel: ChannelRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ChannelRelateToOneForUpdateInput {
  create: ChannelCreateInput
  connect: ChannelWhereUniqueInput
  disconnect: Boolean
}

input CartItemUpdateArgs {
  where: CartItemWhereUniqueInput!
  data: CartItemUpdateInput!
}

input CartItemCreateInput {
  name: String
  image: String
  price: String
  quantity: Int
  productId: String
  variantId: String
  lineItemId: String
  sku: String
  url: String
  error: String
  purchaseId: String
  status: String
  order: OrderRelateToOneForCreateInput
  trackingDetails: TrackingDetailRelateToManyForCreateInput
  user: UserRelateToOneForCreateInput
  channel: ChannelRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ChannelRelateToOneForCreateInput {
  create: ChannelCreateInput
  connect: ChannelWhereUniqueInput
}

type Channel {
  id: ID!
  name: String
  domain: String
  accessToken: String
  links(where: LinkWhereInput! = {}, orderBy: [LinkOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: LinkWhereUniqueInput): [Link!]
  linksCount(where: LinkWhereInput! = {}): Int
  platform: ChannelPlatform
  channelItems(where: ChannelItemWhereInput! = {}, orderBy: [ChannelItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ChannelItemWhereUniqueInput): [ChannelItem!]
  channelItemsCount(where: ChannelItemWhereInput! = {}): Int
  cartItems(where: CartItemWhereInput! = {}, orderBy: [CartItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CartItemWhereUniqueInput): [CartItem!]
  cartItemsCount(where: CartItemWhereInput! = {}): Int
  metadata: JSON
  user: User
  createdAt: DateTime
  updatedAt: DateTime
}

input ChannelWhereUniqueInput {
  id: ID
}

input ChannelWhereInput {
  AND: [ChannelWhereInput!]
  OR: [ChannelWhereInput!]
  NOT: [ChannelWhereInput!]
  id: IDFilter
  name: StringFilter
  domain: StringFilter
  accessToken: StringFilter
  links: LinkManyRelationFilter
  platform: ChannelPlatformWhereInput
  channelItems: ChannelItemManyRelationFilter
  cartItems: CartItemManyRelationFilter
  user: UserWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ChannelOrderByInput {
  id: OrderDirection
  name: OrderDirection
  domain: OrderDirection
  accessToken: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ChannelUpdateInput {
  name: String
  domain: String
  accessToken: String
  links: LinkRelateToManyForUpdateInput
  platform: ChannelPlatformRelateToOneForUpdateInput
  channelItems: ChannelItemRelateToManyForUpdateInput
  cartItems: CartItemRelateToManyForUpdateInput
  metadata: JSON
  user: UserRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ChannelPlatformRelateToOneForUpdateInput {
  create: ChannelPlatformCreateInput
  connect: ChannelPlatformWhereUniqueInput
  disconnect: Boolean
}

input ChannelUpdateArgs {
  where: ChannelWhereUniqueInput!
  data: ChannelUpdateInput!
}

input ChannelCreateInput {
  name: String
  domain: String
  accessToken: String
  links: LinkRelateToManyForCreateInput
  platform: ChannelPlatformRelateToOneForCreateInput
  channelItems: ChannelItemRelateToManyForCreateInput
  cartItems: CartItemRelateToManyForCreateInput
  metadata: JSON
  user: UserRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ChannelPlatformRelateToOneForCreateInput {
  create: ChannelPlatformCreateInput
  connect: ChannelPlatformWhereUniqueInput
}

type ChannelPlatform {
  id: ID!
  name: String
  appKey: String
  appSecret: String
  callbackUrl: String
  createPurchaseFunction: String
  searchProductsFunction: String
  getProductFunction: String
  getWebhooksFunction: String
  deleteWebhookFunction: String
  createWebhookFunction: String
  cancelPurchaseWebhookHandler: String
  createTrackingWebhookHandler: String
  oAuthFunction: String
  oAuthCallbackFunction: String
  channels(where: ChannelWhereInput! = {}, orderBy: [ChannelOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ChannelWhereUniqueInput): [Channel!]
  channelsCount(where: ChannelWhereInput! = {}): Int
  user: User
  createdAt: DateTime
  updatedAt: DateTime
}

input ChannelPlatformWhereUniqueInput {
  id: ID
}

input ChannelPlatformWhereInput {
  AND: [ChannelPlatformWhereInput!]
  OR: [ChannelPlatformWhereInput!]
  NOT: [ChannelPlatformWhereInput!]
  id: IDFilter
  name: StringFilter
  appKey: StringFilter
  appSecret: StringFilter
  createPurchaseFunction: StringFilter
  searchProductsFunction: StringFilter
  getProductFunction: StringFilter
  getWebhooksFunction: StringFilter
  deleteWebhookFunction: StringFilter
  createWebhookFunction: StringFilter
  cancelPurchaseWebhookHandler: StringFilter
  createTrackingWebhookHandler: StringFilter
  oAuthFunction: StringFilter
  oAuthCallbackFunction: StringFilter
  channels: ChannelManyRelationFilter
  user: UserWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ChannelPlatformOrderByInput {
  id: OrderDirection
  name: OrderDirection
  appKey: OrderDirection
  appSecret: OrderDirection
  createPurchaseFunction: OrderDirection
  searchProductsFunction: OrderDirection
  getProductFunction: OrderDirection
  getWebhooksFunction: OrderDirection
  deleteWebhookFunction: OrderDirection
  createWebhookFunction: OrderDirection
  cancelPurchaseWebhookHandler: OrderDirection
  createTrackingWebhookHandler: OrderDirection
  oAuthFunction: OrderDirection
  oAuthCallbackFunction: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ChannelPlatformUpdateInput {
  name: String
  appKey: String
  appSecret: String
  createPurchaseFunction: String
  searchProductsFunction: String
  getProductFunction: String
  getWebhooksFunction: String
  deleteWebhookFunction: String
  createWebhookFunction: String
  cancelPurchaseWebhookHandler: String
  createTrackingWebhookHandler: String
  oAuthFunction: String
  oAuthCallbackFunction: String
  channels: ChannelRelateToManyForUpdateInput
  user: UserRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ChannelPlatformUpdateArgs {
  where: ChannelPlatformWhereUniqueInput!
  data: ChannelPlatformUpdateInput!
}

input ChannelPlatformCreateInput {
  name: String
  appKey: String
  appSecret: String
  createPurchaseFunction: String
  searchProductsFunction: String
  getProductFunction: String
  getWebhooksFunction: String
  deleteWebhookFunction: String
  createWebhookFunction: String
  cancelPurchaseWebhookHandler: String
  createTrackingWebhookHandler: String
  oAuthFunction: String
  oAuthCallbackFunction: String
  channels: ChannelRelateToManyForCreateInput
  user: UserRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type ChannelItem {
  id: ID!
  quantity: Int
  productId: String
  variantId: String
  lineItemId: String
  price: String
  priceChanged: Float
  externalDetails: ChannelProduct
  matches(where: MatchWhereInput! = {}, orderBy: [MatchOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: MatchWhereUniqueInput): [Match!]
  matchesCount(where: MatchWhereInput! = {}): Int
  channel: Channel
  user: User
  createdAt: DateTime
  updatedAt: DateTime
}

type ChannelProduct {
  image: String
  title: String
  productId: ID
  variantId: ID
  price: String
  availableForSale: Boolean
  productLink: String
  inventory: Int
  inventoryTracked: Boolean
  error: String
}

input ChannelItemWhereUniqueInput {
  id: ID
}

input ChannelItemWhereInput {
  AND: [ChannelItemWhereInput!]
  OR: [ChannelItemWhereInput!]
  NOT: [ChannelItemWhereInput!]
  id: IDFilter
  quantity: IntNullableFilter
  productId: StringFilter
  variantId: StringFilter
  lineItemId: StringFilter
  price: StringFilter
  matches: MatchManyRelationFilter
  channel: ChannelWhereInput
  user: UserWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ChannelItemOrderByInput {
  id: OrderDirection
  quantity: OrderDirection
  productId: OrderDirection
  variantId: OrderDirection
  lineItemId: OrderDirection
  price: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ChannelItemUpdateInput {
  quantity: Int
  productId: String
  variantId: String
  lineItemId: String
  price: String
  matches: MatchRelateToManyForUpdateInput
  channel: ChannelRelateToOneForUpdateInput
  user: UserRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ChannelItemUpdateArgs {
  where: ChannelItemWhereUniqueInput!
  data: ChannelItemUpdateInput!
}

input ChannelItemCreateInput {
  quantity: Int
  productId: String
  variantId: String
  lineItemId: String
  price: String
  matches: MatchRelateToManyForCreateInput
  channel: ChannelRelateToOneForCreateInput
  user: UserRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type Shop {
  id: ID!
  name: String
  domain: String
  accessToken: String
  linkMode: ShopLinkModeType
  links(where: LinkWhereInput! = {}, orderBy: [LinkOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: LinkWhereUniqueInput): [Link!]
  linksCount(where: LinkWhereInput! = {}): Int
  platform: ShopPlatform
  orders(where: OrderWhereInput! = {}, orderBy: [OrderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: OrderWhereUniqueInput): [Order!]
  ordersCount(where: OrderWhereInput! = {}): Int
  shopItems(where: ShopItemWhereInput! = {}, orderBy: [ShopItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShopItemWhereUniqueInput): [ShopItem!]
  shopItemsCount(where: ShopItemWhereInput! = {}): Int
  metadata: JSON
  user: User
  createdAt: DateTime
  updatedAt: DateTime
}

enum ShopLinkModeType {
  sequential
  simultaneous
}

input ShopWhereUniqueInput {
  id: ID
}

input ShopWhereInput {
  AND: [ShopWhereInput!]
  OR: [ShopWhereInput!]
  NOT: [ShopWhereInput!]
  id: IDFilter
  name: StringFilter
  domain: StringFilter
  accessToken: StringFilter
  linkMode: ShopLinkModeTypeNullableFilter
  links: LinkManyRelationFilter
  platform: ShopPlatformWhereInput
  orders: OrderManyRelationFilter
  shopItems: ShopItemManyRelationFilter
  user: UserWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ShopLinkModeTypeNullableFilter {
  equals: ShopLinkModeType
  in: [ShopLinkModeType!]
  notIn: [ShopLinkModeType!]
  not: ShopLinkModeTypeNullableFilter
}

input ShopOrderByInput {
  id: OrderDirection
  name: OrderDirection
  domain: OrderDirection
  accessToken: OrderDirection
  linkMode: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ShopUpdateInput {
  name: String
  domain: String
  accessToken: String
  linkMode: ShopLinkModeType
  links: LinkRelateToManyForUpdateInput
  platform: ShopPlatformRelateToOneForUpdateInput
  orders: OrderRelateToManyForUpdateInput
  shopItems: ShopItemRelateToManyForUpdateInput
  metadata: JSON
  user: UserRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ShopPlatformRelateToOneForUpdateInput {
  create: ShopPlatformCreateInput
  connect: ShopPlatformWhereUniqueInput
  disconnect: Boolean
}

input ShopUpdateArgs {
  where: ShopWhereUniqueInput!
  data: ShopUpdateInput!
}

input ShopCreateInput {
  name: String
  domain: String
  accessToken: String
  linkMode: ShopLinkModeType
  links: LinkRelateToManyForCreateInput
  platform: ShopPlatformRelateToOneForCreateInput
  orders: OrderRelateToManyForCreateInput
  shopItems: ShopItemRelateToManyForCreateInput
  metadata: JSON
  user: UserRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ShopPlatformRelateToOneForCreateInput {
  create: ShopPlatformCreateInput
  connect: ShopPlatformWhereUniqueInput
}

type ShopPlatform {
  id: ID!
  name: String
  appKey: String
  appSecret: String
  callbackUrl: String
  orderLinkFunction: String
  updateProductFunction: String
  getWebhooksFunction: String
  deleteWebhookFunction: String
  createWebhookFunction: String
  searchProductsFunction: String
  getProductFunction: String
  searchOrdersFunction: String
  addTrackingFunction: String
  addCartToPlatformOrderFunction: String
  cancelOrderWebhookHandler: String
  createOrderWebhookHandler: String
  oAuthFunction: String
  oAuthCallbackFunction: String
  shops(where: ShopWhereInput! = {}, orderBy: [ShopOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShopWhereUniqueInput): [Shop!]
  shopsCount(where: ShopWhereInput! = {}): Int
  user: User
  createdAt: DateTime
  updatedAt: DateTime
}

input ShopPlatformWhereUniqueInput {
  id: ID
}

input ShopPlatformWhereInput {
  AND: [ShopPlatformWhereInput!]
  OR: [ShopPlatformWhereInput!]
  NOT: [ShopPlatformWhereInput!]
  id: IDFilter
  name: StringFilter
  appKey: StringFilter
  appSecret: StringFilter
  orderLinkFunction: StringFilter
  updateProductFunction: StringFilter
  getWebhooksFunction: StringFilter
  deleteWebhookFunction: StringFilter
  createWebhookFunction: StringFilter
  searchProductsFunction: StringFilter
  getProductFunction: StringFilter
  searchOrdersFunction: StringFilter
  addTrackingFunction: StringFilter
  addCartToPlatformOrderFunction: StringFilter
  cancelOrderWebhookHandler: StringFilter
  createOrderWebhookHandler: StringFilter
  oAuthFunction: StringFilter
  oAuthCallbackFunction: StringFilter
  shops: ShopManyRelationFilter
  user: UserWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ShopPlatformOrderByInput {
  id: OrderDirection
  name: OrderDirection
  appKey: OrderDirection
  appSecret: OrderDirection
  orderLinkFunction: OrderDirection
  updateProductFunction: OrderDirection
  getWebhooksFunction: OrderDirection
  deleteWebhookFunction: OrderDirection
  createWebhookFunction: OrderDirection
  searchProductsFunction: OrderDirection
  getProductFunction: OrderDirection
  searchOrdersFunction: OrderDirection
  addTrackingFunction: OrderDirection
  addCartToPlatformOrderFunction: OrderDirection
  cancelOrderWebhookHandler: OrderDirection
  createOrderWebhookHandler: OrderDirection
  oAuthFunction: OrderDirection
  oAuthCallbackFunction: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ShopPlatformUpdateInput {
  name: String
  appKey: String
  appSecret: String
  orderLinkFunction: String
  updateProductFunction: String
  getWebhooksFunction: String
  deleteWebhookFunction: String
  createWebhookFunction: String
  searchProductsFunction: String
  getProductFunction: String
  searchOrdersFunction: String
  addTrackingFunction: String
  addCartToPlatformOrderFunction: String
  cancelOrderWebhookHandler: String
  createOrderWebhookHandler: String
  oAuthFunction: String
  oAuthCallbackFunction: String
  shops: ShopRelateToManyForUpdateInput
  user: UserRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ShopPlatformUpdateArgs {
  where: ShopPlatformWhereUniqueInput!
  data: ShopPlatformUpdateInput!
}

input ShopPlatformCreateInput {
  name: String
  appKey: String
  appSecret: String
  orderLinkFunction: String
  updateProductFunction: String
  getWebhooksFunction: String
  deleteWebhookFunction: String
  createWebhookFunction: String
  searchProductsFunction: String
  getProductFunction: String
  searchOrdersFunction: String
  addTrackingFunction: String
  addCartToPlatformOrderFunction: String
  cancelOrderWebhookHandler: String
  createOrderWebhookHandler: String
  oAuthFunction: String
  oAuthCallbackFunction: String
  shops: ShopRelateToManyForCreateInput
  user: UserRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type ShopItem {
  id: ID!
  quantity: Int
  productId: String
  variantId: String
  lineItemId: String
  externalDetails: ShopProduct
  matches(where: MatchWhereInput! = {}, orderBy: [MatchOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: MatchWhereUniqueInput): [Match!]
  matchesCount(where: MatchWhereInput! = {}): Int
  shop: Shop
  user: User
  createdAt: DateTime
  updatedAt: DateTime
}

type ShopProduct {
  image: String
  title: String
  productId: ID
  variantId: ID
  price: String
  availableForSale: Boolean
  productLink: String
  inventory: Int
  inventoryTracked: Boolean
  error: String
}

input ShopItemWhereUniqueInput {
  id: ID
}

input ShopItemWhereInput {
  AND: [ShopItemWhereInput!]
  OR: [ShopItemWhereInput!]
  NOT: [ShopItemWhereInput!]
  id: IDFilter
  quantity: IntNullableFilter
  productId: StringFilter
  variantId: StringFilter
  lineItemId: StringFilter
  matches: MatchManyRelationFilter
  shop: ShopWhereInput
  user: UserWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ShopItemOrderByInput {
  id: OrderDirection
  quantity: OrderDirection
  productId: OrderDirection
  variantId: OrderDirection
  lineItemId: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ShopItemUpdateInput {
  quantity: Int
  productId: String
  variantId: String
  lineItemId: String
  matches: MatchRelateToManyForUpdateInput
  shop: ShopRelateToOneForUpdateInput
  user: UserRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ShopItemUpdateArgs {
  where: ShopItemWhereUniqueInput!
  data: ShopItemUpdateInput!
}

input ShopItemCreateInput {
  quantity: Int
  productId: String
  variantId: String
  lineItemId: String
  matches: MatchRelateToManyForCreateInput
  shop: ShopRelateToOneForCreateInput
  user: UserRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type Match {
  id: ID!
  input(where: ShopItemWhereInput! = {}, orderBy: [ShopItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShopItemWhereUniqueInput): [ShopItem!]
  inputCount(where: ShopItemWhereInput! = {}): Int
  output(where: ChannelItemWhereInput! = {}, orderBy: [ChannelItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ChannelItemWhereUniqueInput): [ChannelItem!]
  outputCount(where: ChannelItemWhereInput! = {}): Int
  user: User
  outputPriceChanged: Float
  inventoryNeedsToBeSynced: MatchInventoryData
  createdAt: DateTime
  updatedAt: DateTime
}

type MatchInventoryData {
  syncEligible: Boolean
  sourceQuantity: Int
  targetQuantity: Int
  syncNeeded: Boolean
}

input MatchWhereUniqueInput {
  id: ID
}

input MatchWhereInput {
  AND: [MatchWhereInput!]
  OR: [MatchWhereInput!]
  NOT: [MatchWhereInput!]
  id: IDFilter
  input: ShopItemManyRelationFilter
  output: ChannelItemManyRelationFilter
  user: UserWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input MatchOrderByInput {
  id: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input MatchUpdateInput {
  input: ShopItemRelateToManyForUpdateInput
  output: ChannelItemRelateToManyForUpdateInput
  user: UserRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input MatchUpdateArgs {
  where: MatchWhereUniqueInput!
  data: MatchUpdateInput!
}

input MatchCreateInput {
  input: ShopItemRelateToManyForCreateInput
  output: ChannelItemRelateToManyForCreateInput
  user: UserRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type Link {
  id: ID!
  shop: Shop
  channel: Channel
  rank: Int
  filters: JSON
  customWhere: JSON
  dynamicWhereClause: JSON
  user: User
  createdAt: DateTime
  updatedAt: DateTime
}

input LinkWhereUniqueInput {
  id: ID
}

input LinkWhereInput {
  AND: [LinkWhereInput!]
  OR: [LinkWhereInput!]
  NOT: [LinkWhereInput!]
  id: IDFilter
  shop: ShopWhereInput
  channel: ChannelWhereInput
  rank: IntNullableFilter
  user: UserWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input LinkOrderByInput {
  id: OrderDirection
  rank: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input LinkUpdateInput {
  shop: ShopRelateToOneForUpdateInput
  channel: ChannelRelateToOneForUpdateInput
  rank: Int
  filters: JSON
  customWhere: JSON
  user: UserRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input LinkUpdateArgs {
  where: LinkWhereUniqueInput!
  data: LinkUpdateInput!
}

input LinkCreateInput {
  shop: ShopRelateToOneForCreateInput
  channel: ChannelRelateToOneForCreateInput
  rank: Int
  filters: JSON
  customWhere: JSON
  user: UserRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSON @specifiedBy(url: "http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf")

type Mutation {
  createUser(data: UserCreateInput!): User
  createUsers(data: [UserCreateInput!]!): [User]
  updateUser(where: UserWhereUniqueInput!, data: UserUpdateInput!): User
  updateUsers(data: [UserUpdateArgs!]!): [User]
  deleteUser(where: UserWhereUniqueInput!): User
  deleteUsers(where: [UserWhereUniqueInput!]!): [User]
  createapiKey(data: apiKeyCreateInput!): apiKey
  createApiKeys(data: [apiKeyCreateInput!]!): [apiKey]
  updateapiKey(where: apiKeyWhereUniqueInput!, data: apiKeyUpdateInput!): apiKey
  updateApiKeys(data: [apiKeyUpdateArgs!]!): [apiKey]
  deleteapiKey(where: apiKeyWhereUniqueInput!): apiKey
  deleteApiKeys(where: [apiKeyWhereUniqueInput!]!): [apiKey]
  createRole(data: RoleCreateInput!): Role
  createRoles(data: [RoleCreateInput!]!): [Role]
  updateRole(where: RoleWhereUniqueInput!, data: RoleUpdateInput!): Role
  updateRoles(data: [RoleUpdateArgs!]!): [Role]
  deleteRole(where: RoleWhereUniqueInput!): Role
  deleteRoles(where: [RoleWhereUniqueInput!]!): [Role]
  createOrder(data: OrderCreateInput!): Order
  createOrders(data: [OrderCreateInput!]!): [Order]
  updateOrder(where: OrderWhereUniqueInput!, data: OrderUpdateInput!): Order
  updateOrders(data: [OrderUpdateArgs!]!): [Order]
  deleteOrder(where: OrderWhereUniqueInput!): Order
  deleteOrders(where: [OrderWhereUniqueInput!]!): [Order]
  createTrackingDetail(data: TrackingDetailCreateInput!): TrackingDetail
  createTrackingDetails(data: [TrackingDetailCreateInput!]!): [TrackingDetail]
  updateTrackingDetail(where: TrackingDetailWhereUniqueInput!, data: TrackingDetailUpdateInput!): TrackingDetail
  updateTrackingDetails(data: [TrackingDetailUpdateArgs!]!): [TrackingDetail]
  deleteTrackingDetail(where: TrackingDetailWhereUniqueInput!): TrackingDetail
  deleteTrackingDetails(where: [TrackingDetailWhereUniqueInput!]!): [TrackingDetail]
  createLineItem(data: LineItemCreateInput!): LineItem
  createLineItems(data: [LineItemCreateInput!]!): [LineItem]
  updateLineItem(where: LineItemWhereUniqueInput!, data: LineItemUpdateInput!): LineItem
  updateLineItems(data: [LineItemUpdateArgs!]!): [LineItem]
  deleteLineItem(where: LineItemWhereUniqueInput!): LineItem
  deleteLineItems(where: [LineItemWhereUniqueInput!]!): [LineItem]
  createCartItem(data: CartItemCreateInput!): CartItem
  createCartItems(data: [CartItemCreateInput!]!): [CartItem]
  updateCartItem(where: CartItemWhereUniqueInput!, data: CartItemUpdateInput!): CartItem
  updateCartItems(data: [CartItemUpdateArgs!]!): [CartItem]
  deleteCartItem(where: CartItemWhereUniqueInput!): CartItem
  deleteCartItems(where: [CartItemWhereUniqueInput!]!): [CartItem]
  createChannel(data: ChannelCreateInput!): Channel
  createChannels(data: [ChannelCreateInput!]!): [Channel]
  updateChannel(where: ChannelWhereUniqueInput!, data: ChannelUpdateInput!): Channel
  updateChannels(data: [ChannelUpdateArgs!]!): [Channel]
  deleteChannel(where: ChannelWhereUniqueInput!): Channel
  deleteChannels(where: [ChannelWhereUniqueInput!]!): [Channel]
  createChannelPlatform(data: ChannelPlatformCreateInput!): ChannelPlatform
  createChannelPlatforms(data: [ChannelPlatformCreateInput!]!): [ChannelPlatform]
  updateChannelPlatform(where: ChannelPlatformWhereUniqueInput!, data: ChannelPlatformUpdateInput!): ChannelPlatform
  updateChannelPlatforms(data: [ChannelPlatformUpdateArgs!]!): [ChannelPlatform]
  deleteChannelPlatform(where: ChannelPlatformWhereUniqueInput!): ChannelPlatform
  deleteChannelPlatforms(where: [ChannelPlatformWhereUniqueInput!]!): [ChannelPlatform]
  createChannelItem(data: ChannelItemCreateInput!): ChannelItem
  createChannelItems(data: [ChannelItemCreateInput!]!): [ChannelItem]
  updateChannelItem(where: ChannelItemWhereUniqueInput!, data: ChannelItemUpdateInput!): ChannelItem
  updateChannelItems(data: [ChannelItemUpdateArgs!]!): [ChannelItem]
  deleteChannelItem(where: ChannelItemWhereUniqueInput!): ChannelItem
  deleteChannelItems(where: [ChannelItemWhereUniqueInput!]!): [ChannelItem]
  createShop(data: ShopCreateInput!): Shop
  createShops(data: [ShopCreateInput!]!): [Shop]
  updateShop(where: ShopWhereUniqueInput!, data: ShopUpdateInput!): Shop
  updateShops(data: [ShopUpdateArgs!]!): [Shop]
  deleteShop(where: ShopWhereUniqueInput!): Shop
  deleteShops(where: [ShopWhereUniqueInput!]!): [Shop]
  createShopPlatform(data: ShopPlatformCreateInput!): ShopPlatform
  createShopPlatforms(data: [ShopPlatformCreateInput!]!): [ShopPlatform]
  updateShopPlatform(where: ShopPlatformWhereUniqueInput!, data: ShopPlatformUpdateInput!): ShopPlatform
  updateShopPlatforms(data: [ShopPlatformUpdateArgs!]!): [ShopPlatform]
  deleteShopPlatform(where: ShopPlatformWhereUniqueInput!): ShopPlatform
  deleteShopPlatforms(where: [ShopPlatformWhereUniqueInput!]!): [ShopPlatform]
  createShopItem(data: ShopItemCreateInput!): ShopItem
  createShopItems(data: [ShopItemCreateInput!]!): [ShopItem]
  updateShopItem(where: ShopItemWhereUniqueInput!, data: ShopItemUpdateInput!): ShopItem
  updateShopItems(data: [ShopItemUpdateArgs!]!): [ShopItem]
  deleteShopItem(where: ShopItemWhereUniqueInput!): ShopItem
  deleteShopItems(where: [ShopItemWhereUniqueInput!]!): [ShopItem]
  createMatch(data: MatchCreateInput!): Match
  createMatches(data: [MatchCreateInput!]!): [Match]
  updateMatch(where: MatchWhereUniqueInput!, data: MatchUpdateInput!): Match
  updateMatches(data: [MatchUpdateArgs!]!): [Match]
  deleteMatch(where: MatchWhereUniqueInput!): Match
  deleteMatches(where: [MatchWhereUniqueInput!]!): [Match]
  createLink(data: LinkCreateInput!): Link
  createLinks(data: [LinkCreateInput!]!): [Link]
  updateLink(where: LinkWhereUniqueInput!, data: LinkUpdateInput!): Link
  updateLinks(data: [LinkUpdateArgs!]!): [Link]
  deleteLink(where: LinkWhereUniqueInput!): Link
  deleteLinks(where: [LinkWhereUniqueInput!]!): [Link]
  endSession: Boolean!
  authenticateUserWithPassword(email: String!, password: String!): UserAuthenticationWithPasswordResult
  createInitialUser(data: CreateInitialUserInput!): UserAuthenticationWithPasswordSuccess!
  sendUserPasswordResetLink(email: String!): Boolean!
  redeemUserPasswordResetToken(email: String!, token: String!, password: String!): RedeemUserPasswordResetTokenResult
  addToCart(channelId: ID, image: String, name: String, price: String, productId: String, variantId: String, quantity: String, orderId: ID): Order
  placeOrders(ids: [ID!]!): [Order]
  addMatchToCart(orderId: ID!): Order
  matchOrder(orderId: ID!): Match
  overwriteMatch(input: [ShopItemWhereInput!], output: [ChannelItemWhereInput!]): Match
  cancelPurchase(purchaseId: String!): String
  cancelOrder(orderId: String!): String
  createShopWebhook(shopId: ID!, topic: String!, endpoint: String!): CreateWebhookResponse
  deleteShopWebhook(shopId: ID!, webhookId: ID!): DeleteWebhookResponse
  updateShopProduct(shopId: ID!, variantId: ID!, productId: ID!, price: String, inventoryDelta: Int): UpdateProductResponse
  createChannelWebhook(channelId: ID!, topic: String!, endpoint: String!): CreateWebhookResponse
  deleteChannelWebhook(channelId: ID!, webhookId: ID!): DeleteWebhookResponse
  createChannelPurchase(input: CreatePurchaseInput!): CreatePurchaseResponse
  upsertMatch(data: MatchCreateInput!): Match
}

union UserAuthenticationWithPasswordResult = UserAuthenticationWithPasswordSuccess | UserAuthenticationWithPasswordFailure

type UserAuthenticationWithPasswordSuccess {
  sessionToken: String!
  item: User!
}

type UserAuthenticationWithPasswordFailure {
  message: String!
}

input CreateInitialUserInput {
  name: String
  email: String
  password: String
}

type RedeemUserPasswordResetTokenResult {
  code: PasswordResetRedemptionErrorCode!
  message: String!
}

enum PasswordResetRedemptionErrorCode {
  FAILURE
  TOKEN_EXPIRED
  TOKEN_REDEEMED
}

type Query {
  users(where: UserWhereInput! = {}, orderBy: [UserOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: UserWhereUniqueInput): [User!]
  user(where: UserWhereUniqueInput!): User
  usersCount(where: UserWhereInput! = {}): Int
  apiKeys(where: apiKeyWhereInput! = {}, orderBy: [apiKeyOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: apiKeyWhereUniqueInput): [apiKey!]
  apiKey(where: apiKeyWhereUniqueInput!): apiKey
  apiKeysCount(where: apiKeyWhereInput! = {}): Int
  roles(where: RoleWhereInput! = {}, orderBy: [RoleOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: RoleWhereUniqueInput): [Role!]
  role(where: RoleWhereUniqueInput!): Role
  rolesCount(where: RoleWhereInput! = {}): Int
  orders(where: OrderWhereInput! = {}, orderBy: [OrderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: OrderWhereUniqueInput): [Order!]
  order(where: OrderWhereUniqueInput!): Order
  ordersCount(where: OrderWhereInput! = {}): Int
  trackingDetails(where: TrackingDetailWhereInput! = {}, orderBy: [TrackingDetailOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: TrackingDetailWhereUniqueInput): [TrackingDetail!]
  trackingDetail(where: TrackingDetailWhereUniqueInput!): TrackingDetail
  trackingDetailsCount(where: TrackingDetailWhereInput! = {}): Int
  lineItems(where: LineItemWhereInput! = {}, orderBy: [LineItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: LineItemWhereUniqueInput): [LineItem!]
  lineItem(where: LineItemWhereUniqueInput!): LineItem
  lineItemsCount(where: LineItemWhereInput! = {}): Int
  cartItems(where: CartItemWhereInput! = {}, orderBy: [CartItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CartItemWhereUniqueInput): [CartItem!]
  cartItem(where: CartItemWhereUniqueInput!): CartItem
  cartItemsCount(where: CartItemWhereInput! = {}): Int
  channels(where: ChannelWhereInput! = {}, orderBy: [ChannelOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ChannelWhereUniqueInput): [Channel!]
  channel(where: ChannelWhereUniqueInput!): Channel
  channelsCount(where: ChannelWhereInput! = {}): Int
  channelPlatforms(where: ChannelPlatformWhereInput! = {}, orderBy: [ChannelPlatformOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ChannelPlatformWhereUniqueInput): [ChannelPlatform!]
  channelPlatform(where: ChannelPlatformWhereUniqueInput!): ChannelPlatform
  channelPlatformsCount(where: ChannelPlatformWhereInput! = {}): Int
  channelItems(where: ChannelItemWhereInput! = {}, orderBy: [ChannelItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ChannelItemWhereUniqueInput): [ChannelItem!]
  channelItem(where: ChannelItemWhereUniqueInput!): ChannelItem
  channelItemsCount(where: ChannelItemWhereInput! = {}): Int
  shops(where: ShopWhereInput! = {}, orderBy: [ShopOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShopWhereUniqueInput): [Shop!]
  shop(where: ShopWhereUniqueInput!): Shop
  shopsCount(where: ShopWhereInput! = {}): Int
  shopPlatforms(where: ShopPlatformWhereInput! = {}, orderBy: [ShopPlatformOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShopPlatformWhereUniqueInput): [ShopPlatform!]
  shopPlatform(where: ShopPlatformWhereUniqueInput!): ShopPlatform
  shopPlatformsCount(where: ShopPlatformWhereInput! = {}): Int
  shopItems(where: ShopItemWhereInput! = {}, orderBy: [ShopItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShopItemWhereUniqueInput): [ShopItem!]
  shopItem(where: ShopItemWhereUniqueInput!): ShopItem
  shopItemsCount(where: ShopItemWhereInput! = {}): Int
  matches(where: MatchWhereInput! = {}, orderBy: [MatchOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: MatchWhereUniqueInput): [Match!]
  match(where: MatchWhereUniqueInput!): Match
  matchesCount(where: MatchWhereInput! = {}): Int
  links(where: LinkWhereInput! = {}, orderBy: [LinkOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: LinkWhereUniqueInput): [Link!]
  link(where: LinkWhereUniqueInput!): Link
  linksCount(where: LinkWhereInput! = {}): Int
  keystone: KeystoneMeta!
  authenticatedItem: AuthenticatedItem
  validateUserPasswordResetToken(email: String!, token: String!): ValidateUserPasswordResetTokenResult
  getMatch(input: [ShopItemWhereInput!]): [ChannelItemPlus!]
  getMatchCount(input: [ShopItemWhereInput!]): Int
  redirectToInit: Boolean
  searchShopProducts(shopId: ID!, searchEntry: String): [ShopProduct]
  getShopProduct(shopId: ID!, variantId: String, productId: String): ShopProduct
  searchShopOrders(shopId: ID!, searchEntry: String, take: Int!, skip: Int, after: String): ShopOrderConnection
  getShopWebhooks(shopId: ID!): [Webhook]
  searchChannelProducts(channelId: ID!, searchEntry: String): [ChannelProduct]
  getChannelProduct(channelId: ID!, variantId: String, productId: String): ChannelProduct
  getChannelWebhooks(channelId: ID!): [Webhook]
  getFilteredMatches: [Match]
}

union AuthenticatedItem = User

type ValidateUserPasswordResetTokenResult {
  code: PasswordResetRedemptionErrorCode!
  message: String!
}

type KeystoneMeta {
  adminMeta: KeystoneAdminMeta!
}

type KeystoneAdminMeta {
  lists: [KeystoneAdminUIListMeta!]!
  list(key: String!): KeystoneAdminUIListMeta
}

type KeystoneAdminUIListMeta {
  key: String!
  itemQueryName: String!
  listQueryName: String!
  hideCreate: Boolean!
  hideDelete: Boolean!
  path: String!
  label: String!
  singular: String!
  plural: String!
  description: String
  initialColumns: [String!]!
  pageSize: Int!
  labelField: String!
  fields: [KeystoneAdminUIFieldMeta!]!
  groups: [KeystoneAdminUIFieldGroupMeta!]!
  initialSort: KeystoneAdminUISort
  isHidden: Boolean!
  isSingleton: Boolean!
}

type KeystoneAdminUIFieldMeta {
  path: String!
  label: String!
  description: String
  isOrderable: Boolean!
  isFilterable: Boolean!
  isNonNull: [KeystoneAdminUIFieldMetaIsNonNull!]
  fieldMeta: JSON
  viewsIndex: Int!
  customViewsIndex: Int
  createView: KeystoneAdminUIFieldMetaCreateView!
  listView: KeystoneAdminUIFieldMetaListView!
  itemView(id: ID): KeystoneAdminUIFieldMetaItemView
  search: QueryMode
}

enum KeystoneAdminUIFieldMetaIsNonNull {
  read
  create
  update
}

type KeystoneAdminUIFieldMetaCreateView {
  fieldMode: KeystoneAdminUIFieldMetaCreateViewFieldMode!
}

enum KeystoneAdminUIFieldMetaCreateViewFieldMode {
  edit
  hidden
}

type KeystoneAdminUIFieldMetaListView {
  fieldMode: KeystoneAdminUIFieldMetaListViewFieldMode!
}

enum KeystoneAdminUIFieldMetaListViewFieldMode {
  read
  hidden
}

type KeystoneAdminUIFieldMetaItemView {
  fieldMode: KeystoneAdminUIFieldMetaItemViewFieldMode
  fieldPosition: KeystoneAdminUIFieldMetaItemViewFieldPosition
}

enum KeystoneAdminUIFieldMetaItemViewFieldMode {
  edit
  read
  hidden
}

enum KeystoneAdminUIFieldMetaItemViewFieldPosition {
  form
  sidebar
}

type KeystoneAdminUIFieldGroupMeta {
  label: String!
  description: String
  fields: [KeystoneAdminUIFieldMeta!]!
}

type KeystoneAdminUISort {
  field: String!
  direction: KeystoneAdminUISortDirection!
}

enum KeystoneAdminUISortDirection {
  ASC
  DESC
}

type FoundMatch {
  id: ID!
  output: [ChannelItemPlus!]
}

type ChannelItemPlus {
  quantity: Int
  productId: String
  variantId: String
  price: String
  image: String
  name: String
  channelName: String
  channelId: String
}

type ShopOrder {
  orderId: ID!
  orderName: String
  link: String
  date: String
  firstName: String
  lastName: String
  streetAddress1: String
  streetAddress2: String
  city: String
  state: String
  zip: String
  country: String
  email: String
  cartItems: [ShopCartItem]
  cursor: String
  lineItems: [ShopLineItem]
  fulfillments: [Fulfillment]
  note: String
  totalPrice: String
}

type ShopOrderConnection {
  orders: [ShopOrder]
  hasNextPage: Boolean
}

type ChannelPlus {
  id: ID!
  name: String!
}

type ShopCartItem {
  productId: String
  variantId: String
  quantity: Int
  price: String
  name: String
  image: String
  channel: ChannelPlus
}

type ShopLineItem {
  name: String
  quantity: Int
  price: String
  image: String
  productId: String
  variantId: String
  lineItemId: String
}

type Fulfillment {
  company: String
  number: String
  url: String
}

type Webhook {
  id: ID!
  callbackUrl: String!
  createdAt: DateTime!
  topic: String!
  includeFields: [String!]
}

type CreateWebhookResponse {
  success: Boolean
  error: String
  webhookId: ID
}

type DeleteWebhookResponse {
  success: Boolean
  error: String
}

type UpdateProductResponse {
  success: Boolean
  error: String
  updatedVariant: ProductVariant
}

type ProductVariant {
  price: String
  inventory: Int
}

input CreatePurchaseInput {
  shopId: ID!
  cartItems: [CartItemInput!]!
  email: String!
  address: AddressInput!
  orderId: ID!
}

input CartItemInput {
  variantId: ID!
  quantity: Int!
}

input AddressInput {
  firstName: String!
  lastName: String!
  streetAddress1: String!
  streetAddress2: String
  city: String!
  state: String!
  zip: String!
  country: String!
}

type CreatePurchaseResponse {
  success: Boolean
  error: String
  purchaseId: ID
}

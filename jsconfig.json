{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "baseUrl": ".",
    "paths": {
      "@keystone/*": ["keystone/*"],
      "@shopAdapters/*": ["shopAdapters/*"],
      "@channelAdapters/*": ["channelAdapters/*"],
      "@assets/*": ["assets/*"],
      "@config/*": ["config/*"],
      "@components/*": ["components/*"],
      "@utils/*": ["utils/*"],
      "@primitives/*": ["primitives/*"],
      // we are using the UI in the orion theme for oms so we hardcode this, we should abstract the ui library in the future
      "@ui/*": ["keystone/themes/Tailwind/orion/primitives/default/ui/*"],
      "@pages/*": ["pages/*"],
      "@graphql/*": ["graphql/*"],
      "@svg": ["svg"]
    }
  },
  "include": ["**/*.js"],
  "exclude": ["node_modules"]
}

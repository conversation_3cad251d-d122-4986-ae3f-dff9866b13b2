{"name": "openship", "version": "2.0.0", "private": true, "license": "AGPLv3", "scripts": {"dev": "keystone build --no-ui && yarn migrate && next dev", "build": "keystone build --no-ui && yarn migrate && next build", "start": "next start", "lint": "next lint", "migrate:gen": "keystone build --no-ui && keystone prisma migrate dev && yarn generate-field-views", "migrate": "keystone prisma migrate deploy", "generate-field-views": "tsx keystone/scripts/generate-field-views.js"}, "dependencies": {"@apollo/client": "3.10.6", "@hapi/iron": "^7.0.1", "@headlessui/react": "^2.0.4", "@heroicons/react": "^2.1.3", "@keystone-6/auth": "^8.0.0", "@keystone-6/cloudinary": "^8.0.0", "@keystone-6/core": "6.1.0", "@keystone-6/fields-document": "^9.0.0", "@keystone-ui/core": "^5.0.2", "@keystone-ui/icons": "^6.0.2", "@keystone-ui/loading": "^6.0.2", "@keystone-ui/modals": "^6.0.3", "@keystone-ui/notice": "^6.0.2", "@keystone-ui/options": "^6.0.2", "@keystone-ui/pill": "^7.0.2", "@keystone-ui/popover": "^6.0.2", "@keystone-ui/segmented-control": "^7.0.3", "@keystone-ui/toast": "^6.0.2", "@keystone-ui/tooltip": "^6.0.2", "@preconstruct/next": "^4.0.0", "@primer/octicons-react": "^16.3.1", "@prisma/client": "5.13.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@remixicon/react": "^4.2.0", "@squircle-js/react": "^1.2.0", "@tanstack/react-table": "^8.10.7", "@tanstack/react-virtual": "^3.13.0", "@whitebox-co/walmart-marketplace-api": "^2.2.0", "ag-grid-community": "^31.3.2", "ag-grid-react": "^31.3.2", "apollo-upload-client": "^17.0.0", "boring-avatars": "^1.6.1", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^1.0.4", "cookie": "^0.4.1", "dotenv": "^16.0.0", "fast-deep-equal": "^3.1.3", "form-data": "^4.0.0", "framer-motion": "^11.2.6", "graphql": "^16.8.1", "graphql-request": "^5.0.0", "graphql-upload": "^15.0.2", "graphql-yoga": "^3.1.0", "lodash.isequal": "^4.5.0", "lodash.isplainobject": "^4.0.6", "lodash.reduce": "^4.6.0", "lucide-react": "^0.364.0", "next": "13.4.8", "next-nprogress-bar": "^2.1.2", "next-themes": "^0.2.1", "next-view-transitions": "^0.1.1", "nodemailer": "^6.7.1", "nprogress": "^0.2.0", "react": "18.2.0", "react-country-flag": "^3.1.0", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "react-focus-lock": "^2.12.1", "react-hook-form": "^7.52.0", "react-instantsearch-hooks-web": "^6.43.0", "react-intersection-observer": "^9.4.3", "react-select": "^5.8.0", "react-sortablejs": "^6.1.4", "shopify-token": "^4.0.4", "sonner": "^1.4.41", "sortablejs": "^1.15.2", "stripe": "^9.0.0", "swr": "^1.3.0", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "tailwindcss-hero-patterns": "^0.1.2", "tinycolor2": "^1.6.0", "vaul": "^0.9.6", "xml2js": "^0.4.23"}, "devDependencies": {"autoprefixer": "^10.4.14", "encoding": "^0.1.13", "eslint": "8.22.0", "eslint-config-next": "12.2.5", "postcss": "^8.4.21", "prisma": "5.17.0", "tailwindcss": "^3.3.0", "tsx": "^4.6.2"}}
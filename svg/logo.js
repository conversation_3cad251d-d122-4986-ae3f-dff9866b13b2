export default function Logo({ color = "#fff", ...props }) {
  return (
    <svg viewBox="0 180 495 110" fill="currentColor" {...props}>
      <path
        d="M363.113 225.168v34.25h4.485l.059-14.875.059-14.875.378-1.333c4.174-14.693 22.894-17.61 29.026-4.524 1.598 3.409 1.653 4.117 1.656 21.315l.003 14.292h4.334l-.001-15.292c0-18.154-.014-18.293-2.181-22.592-6.398-12.687-24.211-14.177-32.297-2.701l-.938 1.331-.043-14.623-.042-14.623h-4.498v34.25m58.255-33.16c-3.029 1.152-2.963 5.483.098 6.5 2.973.987 5.588-2.148 4.105-4.923-.76-1.423-2.698-2.15-4.203-1.577m-91.136 19.258c-10.35 1.515-16.239 12.834-10.094 19.403 2.404 2.569 5.247 4.08 10.808 5.745 7.354 2.201 10.19 3.454 12.148 5.368 3.745 3.66 1.265 10.937-4.498 13.203-3.121 1.228-8.388 1.342-11.109.24-2.793-1.131-4.367-2.164-6.672-4.379l-1.722-1.656-1.684 1.684-1.684 1.683.693.828c.711.849 2.302 2.209 3.778 3.229 11.074 7.656 26.697 3.412 28.849-7.836 1.477-7.719-2.752-12.598-13.599-15.691-10.536-3.003-13.267-4.896-13.234-9.169.069-8.786 13.141-11.463 20.564-4.21 1.897 1.854 1.434 1.871 3.423-.128l1.664-1.67-1.417-1.345c-1.304-1.237-3.41-2.697-4.833-3.35-1.965-.902-2.538-1.132-3.611-1.449-2.162-.64-5.393-.847-7.77-.5m131.297.076c-5.072.853-10.561 4.334-13.746 8.716-.41.565-.804 1.027-.875 1.027-.071 0-.129-2.025-.129-4.5v-4.5h-4.5v68.176l2.209-.046 2.208-.047.083-14.917.084-14.916.956 1.269c10.248 13.603 29.494 11.074 37.031-4.867 8.195-17.335-5.661-38.365-23.321-35.395m-351.504.742c-15.57 2.77-24.614 19.791-17.977 33.832 8.692 18.388 33.913 18.949 43.479.967 9.126-17.155-6.307-38.213-25.502-34.799m60.171-.067c-3.553.629-8.208 2.874-10.785 5.203-.896.809-.965.663-.965-2.064v-2.41l-4.792.044-4.791.045v66.666h9.5l.041-12.5c.023-6.875.079-12.6.125-12.722.057-.151.418.068 1.125.683 14.482 12.585 36.608 1.048 36.525-19.044-.046-11.341-6.423-20.026-17.233-23.469-2.092-.666-6.262-.872-8.75-.432m55.083-.178c-.137.027-.812.137-1.5.244-11.226 1.747-19.693 11.758-19.737 23.335-.062 16.186 14.997 27.977 30.64 23.991 2.622-.668 6.26-2.422 8.971-4.324 1.588-1.115 4.793-4.148 4.793-4.536 0-.12-1.377-1.593-3.059-3.274l-3.059-3.055-.732.807c-6.32 6.976-15.075 8.456-21.983 3.716-2.847-1.953-5.834-6.594-5.834-9.065 0-.234 1.824-.26 18.066-.26 16.543 0 18.074-.023 18.17-.272.057-.149.187-1.03.289-1.958 1.559-14.207-8.56-25.541-22.691-25.416-1.146.01-2.196.04-2.334.067m56.334.167c-3.486.625-7 2.269-9.857 4.611-.425.349-.817.634-.87.634-.053 0-.118-.993-.143-2.208l-.047-2.208-4.792-.045-4.791-.044v46.51l4.791-.044 4.792-.044.097-14.25c.06-8.859.161-14.502.268-14.917.42-1.641.612-2.21 1.076-3.191 3.937-8.333 16.619-8.509 20.629-.286 1.142 2.343 1.082 1.437 1.177 17.811l.086 14.833 4.792.044 4.792.044v-14.921c0-17.595-.047-18.134-1.915-22.194-3.456-7.507-11.659-11.646-20.085-10.135m138.5 23.745v23.667h4.333v-47.333h-4.333v23.666m48.583-19.764c15.991 4.474 19.104 27.686 4.993 37.232-13.886 9.394-31.402-6.828-25.88-23.968 3.027-9.397 12.717-15.55 20.887-13.264m-237.583 4.373c4.659.954 9.833 6.53 9.833 10.596 0 .071-6.038.129-13.417.129s-13.416-.063-13.416-.139c0-.335.741-2.353 1.202-3.273 2.981-5.95 8.954-8.714 15.798-7.313m-113.681.575c12.331 3.295 15.12 20.628 4.496 27.938-11.321 7.789-25.727-3.694-21.811-17.387 2.209-7.724 10.038-12.495 17.315-10.551m58.097-.073c9.184 2.318 13.84 13.745 9.21 22.602-5.756 11.009-20.066 10.313-24.972-1.216-4.758-11.18 4.804-24.153 15.762-21.386"
        fill={color}
      />
      <g transform="matrix(1.3, 0, 0, 1.3, 5.39868, 205.029358)">
        <rect
          x={27}
          y={28}
          style={{ fill: color }}
          width={2}
          height={18}
          data-original="#424A60"
          className="active-path"
          data-old_color="#ffffff"
        />
        <rect
          x="37.5"
          y="2.893"
          transform="matrix(0.7071 0.7071 -0.7071 0.7071 20.8223 -23.2696)"
          style={{ fill: color }}
          width={2}
          height="21.213"
          data-original="#424A60"
          className="active-path"
          data-old_color="#ffffff"
        />
        <rect
          x={15}
          y="21.858"
          transform="matrix(0.7071 0.7071 -0.7071 0.7071 30.1421 -0.7696)"
          style={{ fill: color }}
          width={2}
          height="28.284"
          data-original="#424A60"
          className="active-path"
          data-old_color="#ffffff"
        />
        <rect
          x="10.722"
          y="13.5"
          transform="matrix(0.7071 0.7071 -0.7071 0.7071 15.6716 -8.8345)"
          style={{ fill: color }}
          width="15.556"
          height={2}
          data-original="#424A60"
          className="active-path"
          data-old_color="#ffffff"
        />
        <rect
          x="26.893"
          y="32.5"
          transform="matrix(0.7071 0.7071 -0.7071 0.7071 34.6716 -16.7046)"
          style={{ fill: color }}
          width="21.213"
          height={2}
          data-original="#424A60"
          className="active-path"
          data-old_color="#ffffff"
        />
        <circle
          style={{ fill: "#735DD0" }}
          cx={48}
          cy={5}
          r={5}
          data-original="#43B05C"
          data-old_color="#745CD4"
        />
        <circle
          style={{ fill: "#14B5D0" }}
          cx={28}
          cy={48}
          r={5}
          data-original="#7383BF"
          data-old_color="#7383BF"
        />
        <circle
          style={{ fill: "#47B881" }}
          cx={5}
          cy={46}
          r={5}
          data-original="#57D8AB"
          data-old_color="#43BC84"
        />
        <circle
          style={{ fill: "#EC4C47" }}
          cx={12}
          cy={8}
          r={3}
          data-original="#D75A4A"
          data-old_color="#F08F4C"
        />
        <circle
          style={{ fill: "#EBBA16" }}
          cx={44}
          cy={40}
          r={3}
          data-original="#EBBA16"
        />
        <circle
          style={{ fill: "#1070CA" }}
          cx={28}
          cy={24}
          r={7}
          data-original="#4B6DAA"
          data-old_color="#0A89CE"
        />
      </g>
    </svg>
  );
}

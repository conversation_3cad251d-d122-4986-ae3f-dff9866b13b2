<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" xml:space="preserve" viewBox="0.98 0.98 120.02 120.02">
<desc>Created with Fabric.js 4.6.0</desc>
<defs>
</defs>
<g transform="matrix(1 0 0 1 61 61)" id="iklonHS-DcchzY-yNJHKI">
<linearGradient id="SVGID_1179_6" gradientUnits="userSpaceOnUse" gradientTransform="matrix(1 0 0 1 0 0)" x1="7.304347726094554" y1="105.91303955384254" x2="113.73912401615324" y2="15.130432100953124">
<stop offset="0%" style="stop-color:#172B4D;stop-opacity: 1"/>
<stop offset="100%" style="stop-color:#1A174D;stop-opacity: 1"/>
</linearGradient>
<path style="stroke: rgb(25,157,183); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: url(#SVGID_1179_6); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" transform=" translate(-60, -60)" d="M 0 25.45584 C 0 11.396969999999998 11.39697 0 25.45584 0 L 94.54415 0 L 94.54415 0 C 108.60302 0 119.99999 11.39697 119.99999 25.45584 L 119.99999 94.54415 L 119.99999 94.54415 C 119.99999 108.60302 108.60302 119.99999 94.54415 119.99999 L 25.455839999999995 119.99999 L 25.455839999999995 119.99999 C 11.396969999999994 119.99999 -3.552713678800501e-15 108.60302 -3.552713678800501e-15 94.54415 z" stroke-linecap="round"/>
</g>
<g transform="matrix(1.39 0 0 1.39 61 61)" id="Fr7rf8-qtOxKP97xG2DMY">
<g style="" vector-effect="non-scaling-stroke">
		<g transform="matrix(1 0 0 1 1.5 10.5)" id="veYceV9MSt7KuPI6FzTSw">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(100,116,139); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke" transform=" translate(-28, -37)" d="M 27 28 L 29 28 L 29 46 L 27 46 z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 12 -13)" id="KC6DiUdPlm53aYpxSX2LN">
<polyline style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(100,116,139); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke" points="-6.79,8.21 -8.21,6.79 6.79,-8.21 8.21,-6.79 -6.79,8.21 "/>
</g>
		<g transform="matrix(1 0 0 1 -10.5 9.5)" id="M_OAlX9xzpqkgTzOyKzz9">
<polyline style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(100,116,139); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke" points="-9.29,10.71 -10.71,9.29 9.29,-10.71 10.71,-9.29 -9.29,10.71 "/>
</g>
		<g transform="matrix(1 0 0 1 -8 -12)" id="3WFQciJvuNs5g5JTwrEyC">
<polyline style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(100,116,139); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke" points="4.79,6.21 -6.21,-4.79 -4.79,-6.21 6.21,4.79 4.79,6.21 "/>
</g>
		<g transform="matrix(1 0 0 1 11 7)" id="iq5q-koauqFw3Qs6w06cT">
<polyline style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(100,116,139); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke" points="6.79,8.21 -8.21,-6.79 -6.79,-8.21 8.21,6.79 6.79,8.21 "/>
</g>
		<g transform="matrix(1 0 0 1 21.5 -21.5)" id="nFGekwo46kPlpFUkgtBcJ">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(115,93,208); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke" transform=" translate(-48, -5)" d="M 53 5 C 53 7.762 50.761 10 48 10 C 45.239 10 43 7.762 43 5 C 43 2.239 45.239 0 48 0 C 50.761 0 53 2.239 53 5" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 1.5 21.5)" id="NztzU41nVAbBJH2BoFoX5">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(20,181,208); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke" transform=" translate(-28, -48)" d="M 33 48 C 33 50.761 30.761 53 28 53 C 25.239 53 23 50.761 23 48 C 23 45.239 25.239 43 28 43 C 30.761 43 33 45.239 33 48" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -21.5 19.5)" id="pAkDvNbNA0Yb2yKVxmkb0">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(71,184,129); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke" transform=" translate(-5, -46)" d="M 10 46 C 10 48.762 7.761 51 5 51 C 2.239 51 0 48.762 0 46 C 0 43.239 2.239 41 5 41 C 7.761 41 10 43.239 10 46" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -14.5 -18.5)" id="mJd05Y93vv1SZ1Ey6xbmb">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(236,76,71); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke" transform=" translate(-12, -8)" d="M 15 8 C 15 9.657 13.657 11 12 11 C 10.343 11 9 9.657 9 8 C 9 6.343 10.343 5 12 5 C 13.657 5 15 6.343 15 8" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 17.5 13.5)" id="VpGYtoMOZUwVpmKSoCZ-T">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(235,186,22); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke" transform=" translate(-44, -40)" d="M 47 40 C 47 41.657 45.657 43 44 43 C 42.343 43 41 41.657 41 40 C 41 38.343 42.343 37 44 37 C 45.657 37 47 38.343 47 40" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 1.5 -2.5)" id="rig0vTZwK8E7lDub8mH0T">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(16,112,202); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke" transform=" translate(-28, -24)" d="M 35 24 C 35 27.866 31.866 31 28 31 C 24.134 31 21 27.866 21 24 C 21 20.134 24.134 17 28 17 C 31.866 17 35 20.134 35 24" stroke-linecap="round"/>
</g>
</g>
</g>
</svg>
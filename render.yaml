services:
- type: web
  name: openship
  env: node
  plan: free
  buildCommand: yarn; yarn build
  startCommand: yarn start
  envVars:
  - key: SESSION_SECRET
    generateValue: true
  - key: FRONTEND_URL
    fromService:
      type: web
      name: openship
      envVarKey: RENDER_EXTERNAL_URL
  - key: PORT
    value: 3000
  - key: DATABASE_URL
    fromDatabase:
      name: openship-pg
      property: connectionString
databases:
- name: openship-pg
  plan: free

import { User } from "./User";
import { api<PERSON><PERSON> } from "./apiKey";
import { Role } from "./Role";
import { Order } from "./Order";
import { TrackingDetail } from "./TrackingDetail";
import { LineItem } from "./LineItem";
import { CartItem } from "./CartItem";
import { Channel } from "./Channel";
import { ChannelItem } from "./ChannelItem";
import { Shop } from "./Shop";
import { ShopItem } from "./ShopItem";
import { Match } from "./Match";
import { Link } from "./Link";
import { ShopPlatform } from "./ShopPlatform";
import { ChannelPlatform } from "./ChannelPlatform";
// Add other imports here if needed

export const models = {
  User,
  apiKey,
  Role,
  Order,
  TrackingDetail,
  LineItem,
  CartItem,
  Channel,
  ChannelPlatform,
  ChannelItem,
  Shop,
  ShopPlatform,
  ShopItem,
  Match,
  Link,
  // Add other models here as needed
};

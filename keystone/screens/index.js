"use client";

// replace KeystoneUI with KeystoneUI or your theme

import { ListPage } from "@keystone/themes/Tailwind/orion/screens/ListPage";
import { ItemPage } from "@keystone/themes/Tailwind/orion/screens/ItemPage";
import { HomePage } from "@keystone/themes/Tailwind/orion/screens/HomePage";
import { CreateItemPage } from "@keystone/themes/Tailwind/orion/screens/CreateItemPage";
import { SignInPage } from "@keystone/themes/Tailwind/orion/screens/SignInPage";
import { SignUpPage } from "@keystone/themes/Tailwind/orion/screens/SignUpPage";
import { ResetPage } from "@keystone/themes/Tailwind/orion/screens/ResetPage";
import { InitPage } from "@keystone/themes/Tailwind/orion/screens/InitPage";
import { NoAccessPage } from "@keystone/themes/Tailwind/orion/screens/NoAccessPage";

import { DashboardLayout } from "@keystone/themes/Tailwind/orion/components/DashboardLayout";
import { OuterLayout } from "@keystone/themes/Tailwind/orion/components/OuterLayout";

import { LoadingIcon } from "@keystone/themes/Tailwind/orion/components/LoadingIcon";
import { ErrorBoundary } from "@keystone/themes/Tailwind/orion/components/ErrorBoundary";

import { useToasts } from "@keystone/themes/Tailwind/orion/components/Toast";

export {
  ListPage,
  ItemPage,
  HomePage,
  CreateItemPage,
  SignInPage,
  SignUpPage,
  ResetPage,
  InitPage,
  NoAccessPage,
  DashboardLayout,
  OuterLayout,
  LoadingIcon,
  ErrorBoundary,
  useToasts,
};

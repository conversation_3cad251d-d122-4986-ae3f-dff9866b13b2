import { useTheme } from "@keystone-ui/core"

// TODO: Move to theme.
export const widthMap = {
  small: 128,
  medium: 256,
  large: 512,
  full: "100%"
}

export const useInputTokens = ({
  size: sizeKey = "medium",

  // width: widthKey = 'large',
  isMultiline = false,

  shape = "square"
}) => {
  const {
    animation,
    controlSizes,
    fields,
    radii,
    spacing,
    typography
  } = useTheme()

  // const width = widthMap[widthKey];
  const size = controlSizes[sizeKey]

  return {
    background: fields.inputBackground,
    borderColor: fields.inputBorderColor,
    borderRadius: shape === "round" ? radii.full : fields.inputBorderRadius,
    borderWidth: fields.inputBorderWidth,
    fontSize: size.fontSize,
    foreground: fields.inputForeground,
    height: isMultiline ? undefined : size.height,
    lineHeight: isMultiline ? typography.leading.base : `${size.height}px`,
    paddingX: spacing.medium,
    paddingY: isMultiline ? spacing.small : 0,
    placeholder: fields.inputPlaceholder,
    shadow: fields.shadow,

    transition: `
      background-color ${animation.duration100},
      box-shadow ${animation.duration100},
      border-color ${animation.duration100}
    `,

    // width,
    hover: {
      background: fields.hover.inputBackground,
      borderColor: fields.hover.inputBorderColor,
      shadow: fields.hover.shadow,
      foreground: fields.hover.inputForeground
    },

    focus: {
      background: fields.focus.inputBackground,
      borderColor: fields.focus.inputBorderColor,
      shadow: fields.focus.shadow,
      foreground: fields.focus.inputForeground
    },

    invalid: {
      background: fields.invalid.inputBackground,
      borderColor: fields.invalid.inputBorderColor,
      shadow: fields.invalid.shadow,
      foreground: fields.invalid.inputForeground
    },

    disabled: {
      background: fields.disabled.inputBackground,
      borderColor: fields.disabled.inputBorderColor,
      shadow: fields.disabled.shadow,
      foreground: fields.disabled.inputForeground
    }
  }
}

export function useInputStyles({ invalid, tokens }) {
  const styles = {
    appearance: "none",
    backgroundColor: invalid ? tokens.invalid.background : tokens.background,
    borderColor: invalid ? tokens.invalid.borderColor : tokens.borderColor,
    borderRadius: tokens.borderRadius,
    borderStyle: "solid",
    borderWidth: tokens.borderWidth,
    boxShadow: invalid ? tokens.invalid.shadow : tokens.shadow,
    boxSizing: "border-box",
    color: invalid ? tokens.invalid.foreground : tokens.foreground,
    fontSize: tokens.fontSize,
    height: tokens.height,
    lineHeight: tokens.lineHeight,

    // maxWidth: tokens.width,
    outline: 0,

    paddingBottom: tokens.paddingY,
    paddingLeft: tokens.paddingX,
    paddingRight: tokens.paddingX,
    paddingTop: tokens.paddingY,

    // applies to textarea
    resize: "vertical",

    transition: tokens.transition,
    width: "100%",

    ":hover": {
      backgroundColor: tokens.hover.background,
      borderColor: tokens.hover.borderColor,
      boxShadow: tokens.hover.shadow,
      color: tokens.hover.foreground
    },

    ":focus": {
      backgroundColor: tokens.focus.background,
      borderColor: tokens.focus.borderColor,
      boxShadow: tokens.focus.shadow,
      color: tokens.focus.foreground
    },

    ":disabled": {
      backgroundColor: tokens.disabled.background,
      borderColor: tokens.disabled.borderColor,
      boxShadow: tokens.disabled.shadow,
      color: tokens.disabled.foreground
    },

    "&::placeholder": {
      color: tokens.placeholder
    }
  }

  return styles
}


// the fieldView order is based on the schema
// if the schema is changed, yarn generate-field-views should be run to update this file

import * as view0 from "@keystone/views/IDField";
import * as view1 from "@keystone/views/Text";
import * as view2 from "@keystone/views/Password";
import * as view3 from "@keystone/views/Relationship";
import * as view4 from "@keystone/views/Timestamp";
import * as view5 from "@keystone/views/Checkbox";
import * as view6 from "@keystone/views/Virtual";
import * as view7 from "@keystone/views/JSON";
import * as view8 from "@keystone/views/Float";
import * as view9 from "@keystone/views/Integer";
import * as view10 from "@keystone/views/Select";

export const fieldViews = [
  view0,
  view1,
  view2,
  view3,
  view4,
  view5,
  view6,
  view7,
  view8,
  view9,
  view10
];
